import api from './api';
import { launchImageLibrary, launchCamera, ImagePickerResponse, ImageLibraryOptions, CameraOptions } from 'react-native-image-picker';
import { Alert } from 'react-native';
import RNFS from 'react-native-fs';

export interface MediaFile {
  id?: string; // backend media id
  uri: string;
  name: string;
  type: string;
  size: number;
  signedUrl?: string;
  base64?: string;
}

export interface MediaUploadResult {
  success: boolean;
  file?: MediaFile;
  error?: string;
}

class MediaUploadService {
  /**
   * Upload file to backend and get media info
   */
  private async uploadToBackend(file: MediaFile): Promise<MediaFile> {
    const formData = new FormData();
    // React Native FormData requires uri, type, name
    formData.append('file', {
      uri: file.uri,
      type: file.type,
      name: file.name,
    } as any);

    // Upload file to backend
    const uploadResponse = await api.post('/api/media/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    const uploadData = uploadResponse.data;

    // Get signed URL for access
    const signedUrlResponse = await api.get(`/api/media/${uploadData.id}/signed-url?expiresIn=3600`);
    const signedUrlData = signedUrlResponse.data;

    return {
      id: uploadData.id,
      uri: file.uri, // <-- Always return the local file path for the sender
      name: uploadData.original_filename,
      type: uploadData.mime_type,
      size: uploadData.file_size,
      signedUrl: signedUrlData.signedUrl, // Only for preview if needed
    };
  }

  /**
   * Pick an image from the device gallery and upload to backend
   */
  async pickImage(): Promise<MediaUploadResult> {
    try {
      const options: ImageLibraryOptions = {
        mediaType: 'photo',
        includeBase64: false,
        maxWidth: 1024,
        maxHeight: 1024,
        quality: 0.8,
        selectionLimit: 1,
      };

      const response: ImagePickerResponse = await launchImageLibrary(options);

      if (response.didCancel) {
        return { success: false, error: 'User cancelled image selection' };
      }

      if (response.errorCode) {
        return { success: false, error: response.errorMessage || 'Failed to pick image' };
      }

      if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        if (!asset.uri) {
          return { success: false, error: 'No image URI found' };
        }
        const file: MediaFile = {
          uri: asset.uri,
          name: asset.fileName || `image_${Date.now()}.jpg`,
          type: asset.type || 'image/jpeg',
          size: asset.fileSize || 0,
        };
        // Upload to backend
        const mediaFile = await this.uploadToBackend(file);
        return { success: true, file: mediaFile };
      }
      return { success: false, error: 'No image selected' };
    } catch (error) {
      console.error('Error picking/uploading image:', error);
      return { success: false, error: 'Failed to pick or upload image' };
    }
  }

  /**
   * Pick a video from the device gallery and upload to backend
   */
  async pickVideo(): Promise<MediaUploadResult> {
    try {
      const options: ImageLibraryOptions = {
        mediaType: 'video',
        includeBase64: false,
        videoQuality: 'medium',
        selectionLimit: 1,
      };

      const response: ImagePickerResponse = await launchImageLibrary(options);

      if (response.didCancel) {
        return { success: false, error: 'User cancelled video selection' };
      }

      if (response.errorCode) {
        return { success: false, error: response.errorMessage || 'Failed to pick video' };
      }

      if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        if (!asset.uri) {
          return { success: false, error: 'No video URI found' };
        }
        const file: MediaFile = {
          uri: asset.uri,
          name: asset.fileName || `video_${Date.now()}.mp4`,
          type: asset.type || 'video/mp4',
          size: asset.fileSize || 0,
        };
        // Upload to backend
        const mediaFile = await this.uploadToBackend(file);
        return { success: true, file: mediaFile };
      }
      return { success: false, error: 'No video selected' };
    } catch (error) {
      console.error('Error picking/uploading video:', error);
      return { success: false, error: 'Failed to pick or upload video' };
    }
  }

  /**
   * Take a photo using the device camera and upload to backend
   */
  async takePhoto(): Promise<MediaUploadResult> {
    try {
      const options: CameraOptions = {
        mediaType: 'photo',
        includeBase64: false,
        maxWidth: 1024,
        maxHeight: 1024,
        quality: 0.8,
        saveToPhotos: true,
      };
      const response: ImagePickerResponse = await launchCamera(options);
      if (response.didCancel) {
        return { success: false, error: 'User cancelled photo capture' };
      }
      if (response.errorCode) {
        return { success: false, error: response.errorMessage || 'Failed to take photo' };
      }
      if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        if (!asset.uri) {
          return { success: false, error: 'No photo URI found' };
        }
        const file: MediaFile = {
          uri: asset.uri,
          name: asset.fileName || `photo_${Date.now()}.jpg`,
          type: asset.type || 'image/jpeg',
          size: asset.fileSize || 0,
        };
        // Upload to backend
        const mediaFile = await this.uploadToBackend(file);
        return { success: true, file: mediaFile };
      }
      return { success: false, error: 'No photo captured' };
    } catch (error) {
      console.error('Error taking/uploading photo:', error);
      return { success: false, error: 'Failed to take or upload photo' };
    }
  }

  /**
   * Record a video using the device camera and upload to backend
   */
  async recordVideo(): Promise<MediaUploadResult> {
    try {
      const options: CameraOptions = {
        mediaType: 'video',
        includeBase64: false,
        videoQuality: 'medium',
        durationLimit: 60, // 60 seconds max
        saveToPhotos: true,
      };
      const response: ImagePickerResponse = await launchCamera(options);
      if (response.didCancel) {
        return { success: false, error: 'User cancelled video recording' };
      }
      if (response.errorCode) {
        return { success: false, error: response.errorMessage || 'Failed to record video' };
      }
      if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        if (!asset.uri) {
          return { success: false, error: 'No video URI found' };
        }
        const file: MediaFile = {
          uri: asset.uri,
          name: asset.fileName || `video_${Date.now()}.mp4`,
          type: asset.type || 'video/mp4',
          size: asset.fileSize || 0,
        };
        // Upload to backend
        const mediaFile = await this.uploadToBackend(file);
        return { success: true, file: mediaFile };
      }
      return { success: false, error: 'No video recorded' };
    } catch (error) {
      console.error('Error recording/uploading video:', error);
      return { success: false, error: 'Failed to record or upload video' };
    }
  }

  /**
   * Pick a document from the device
   */
  async pickDocument(): Promise<MediaUploadResult> {
    // Document picker is not available in this version
    // We'll implement this later with a compatible library
    return { success: false, error: 'Document picker not available yet' };
  }

  /**
   * Record audio using the device microphone
   * Note: This is a basic implementation using react-native-image-picker for audio
   * For advanced audio recording, consider installing react-native-audio-recorder-player
   */
  async recordAudio(): Promise<MediaUploadResult> {
    try {
      // Use react-native-image-picker for basic audio recording
      const options: ImageLibraryOptions = {
        mediaType: 'mixed', // Allow both photo and video, but we'll filter for audio
        includeBase64: false,
        selectionLimit: 1,
      };

      // For now, we'll use the gallery picker to select audio files
      // In a full implementation, you would use a dedicated audio recording library
      const response: ImagePickerResponse = await launchImageLibrary(options);

      if (response.didCancel) {
        return { success: false, error: 'User cancelled audio selection' };
      }

      if (response.errorCode) {
        return { success: false, error: response.errorMessage || 'Failed to select audio' };
      }

      if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        if (!asset.uri) {
          return { success: false, error: 'No audio URI found' };
        }

        // Check if it's actually an audio file
        if (!asset.type || !asset.type.startsWith('audio/')) {
          return { success: false, error: 'Please select an audio file' };
        }

        const file: MediaFile = {
          uri: asset.uri,
          name: asset.fileName || `audio_${Date.now()}.m4a`,
          type: asset.type || 'audio/m4a',
          size: asset.fileSize || 0,
        };

        // Upload to backend
        const mediaFile = await this.uploadToBackend(file);
        return { success: true, file: mediaFile };
      }

      return { success: false, error: 'No audio selected' };
    } catch (error) {
      console.error('Error recording/uploading audio:', error);
      return { success: false, error: 'Failed to record or upload audio. For full audio recording support, please install react-native-audio-recorder-player.' };
    }
  }

  /**
   * Show media selection options
   */
  async showMediaOptions(): Promise<MediaUploadResult> {
    return new Promise((resolve) => {
      Alert.alert(
        'Select Media',
        'Choose how you want to add media',
        [
          {
            text: 'Photo',
            onPress: () => {
              // Show photo options
              Alert.alert(
                'Photo Options',
                'Choose photo source',
                [
                  {
                    text: 'Camera',
                    onPress: async () => {
                      const result = await this.takePhoto();
                      resolve(result);
                    },
                  },
                  {
                    text: 'Gallery',
                    onPress: async () => {
                      const result = await this.pickImage();
                      resolve(result);
                    },
                  },
                  {
                    text: 'Back',
                    style: 'cancel',
                    onPress: () => {
                      this.showMediaOptions().then(resolve);
                    },
                  },
                ],
                { cancelable: true }
              );
            },
          },
          {
            text: 'Video',
            onPress: () => {
              // Show video options
              Alert.alert(
                'Video Options',
                'Choose video source',
                [
                  {
                    text: 'Record Video',
                    onPress: async () => {
                      const result = await this.recordVideo();
                      resolve(result);
                    },
                  },
                  {
                    text: 'Gallery',
                    onPress: async () => {
                      const result = await this.pickVideo();
                      resolve(result);
                    },
                  },
                  {
                    text: 'Back',
                    style: 'cancel',
                    onPress: () => {
                      this.showMediaOptions().then(resolve);
                    },
                  },
                ],
                { cancelable: true }
              );
            },
          },
          {
            text: 'Audio',
            onPress: async () => {
              const result = await this.recordAudio();
              resolve(result);
            },
          },
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => {
              resolve({ success: false, error: 'User cancelled' });
            },
          },
        ],
        { cancelable: true }
      );
    });
  }

  /**
   * Download a file from a signed URL and save it to a local file path using react-native-fs
   * Returns the local file URI (file://...)
   */
  async downloadMediaToLocal(signedUrl: string, fileName: string): Promise<string> {
    try {
      // Pick a local path in the app's document directory
      const localPath = `${RNFS.DocumentDirectoryPath}/${fileName}`;
      const downloadRes = await RNFS.downloadFile({
        fromUrl: signedUrl,
        toFile: localPath,
      }).promise;
      if (downloadRes.statusCode === 200) {
        return 'file://' + localPath; // Prefix required for <Image source={{uri}} />
      } else {
        throw new Error('Failed to download file, status: ' + downloadRes.statusCode);
      }
    } catch (error) {
      console.error('Failed to download media file:', error);
      throw error;
    }
  }

  /**
   * Get file size in human readable format
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Check if file type is supported
   */
  isSupportedFileType(type: string): boolean {
    const supportedTypes = [
      // Images
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      // Videos
      'video/mp4',
      'video/mov',
      'video/avi',
      'video/quicktime',
      'video/3gpp',
      'video/webm',
      // Audio
      'audio/mp3',
      'audio/mpeg',
      'audio/wav',
      'audio/aac',
      'audio/m4a',
      'audio/ogg',
      'audio/webm',
    ];

    return supportedTypes.includes(type.toLowerCase());
  }

  /**
   * Get file icon based on type
   */
  getFileIcon(type: string): string {
    if (type.startsWith('image/')) {
      return 'image';
    } else if (type.startsWith('video/')) {
      return 'videocam';
    } else if (type.startsWith('audio/')) {
      return 'audiotrack';
    } else {
      return 'attach-file';
    }
  }

  /**
   * Clean up temporary files
   */
  async cleanupTempFiles(uri: string): Promise<void> {
    // No cleanup needed for image picker files
    console.log('File cleanup not needed for:', uri);
  }
}

export const mediaUploadService = new MediaUploadService();