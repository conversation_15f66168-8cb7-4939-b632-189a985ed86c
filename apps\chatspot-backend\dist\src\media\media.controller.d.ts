import { MediaService } from './media.service';
import { Media } from './media.entity';
export declare class MediaController {
    private readonly mediaService;
    constructor(mediaService: MediaService);
    uploadMedia(file: Express.Multer.File, req: {
        user: {
            username: string;
        };
    }): Promise<Media>;
    getMediaSignedUrl(id: string, expiresIn?: string): Promise<{
        media: Media;
        signedUrl: string;
        expiresAt: Date;
    }>;
    getMediaInfo(id: string): Promise<Media>;
    deleteMedia(id: string, req: {
        user: {
            username: string;
        };
    }): Promise<{
        message: string;
    }>;
}
