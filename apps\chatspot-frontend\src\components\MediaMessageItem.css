/* Media message wrapper styling - ensure it inherits from regular messages */
.message.media-message {
  max-width: 90%;
  margin: 2px 0;
}

@media (min-width: 768px) {
  .message.media-message {
    max-width: 75%;
  }
}

/* Sent and received alignment - these should inherit from the main message styles */
.message.media-message.sent {
  align-self: flex-end;
}

.message.media-message.received {
  align-self: flex-start;
}

.message.media-message .message-content {
  max-width: 300px;
  padding: 8px 18px;
  border-radius: 10px;
  position: relative;
}

@media (min-width: 768px) {
  .message.media-message .message-content {
    padding: 8px 18px;
    min-width: 65px;
  }
}

/* Sent media message styling */
.message.media-message.sent .message-content {
  background-color: var(--primary-color-tint);
  border-bottom-right-radius: 0;
  box-shadow: 0 0 3px var(--primary-color-light);
  text-align: right;
}

/* Received media message styling */
.message.media-message.received .message-content {
  background-color: var(--tint-color-light);
  border-bottom-left-radius: 0;
  box-shadow: 0 0 3px var(--shade-color-light);
  text-align: left;
}

/* Media caption styling */
.media-caption {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
}

.message.media-message.sent .media-caption {
  color: var(--primary-color);
  text-align: right;
}

.message.media-message.received .media-caption {
  color: var(--shade-color-two);
  text-align: left;
}

/* Message info styling for media messages */
.message.media-message .message-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 2px;
}

.message.media-message.sent .message-info {
  justify-content: flex-end;
}

.message.media-message.received .message-info {
  justify-content: flex-start;
}

.message.media-message .message-time {
  font-size: 0.7em;
  color: var(--text-secondary);
}

.media-content {
  border-radius: 8px;
  overflow: hidden;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

/* Image styles */
.media-image-container {
  position: relative;
  width: 100%;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-image {
  width: 100%;
  height: auto;
  max-height: 300px;
  object-fit: cover;
  border-radius: 8px;
}

.media-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e9ecef;
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.media-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #6c757d;
  text-align: center;
}

.media-error span {
  font-size: 24px;
  margin-bottom: 8px;
}

.media-error p {
  margin: 0;
  font-size: 12px;
}

/* Document styles */
.media-document {
  display: flex;
  align-items: center;
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 8px;
}

.media-document:hover {
  background-color: #e9ecef;
}

.document-icon {
  font-size: 24px;
  margin-right: 12px;
  flex-shrink: 0;
}

.document-info {
  flex: 1;
  min-width: 0;
}

.document-name {
  font-size: 14px;
  font-weight: 500;
  color: #212529;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.document-size {
  font-size: 12px;
  color: #6c757d;
}

.download-icon {
  font-size: 16px;
  margin-left: 8px;
  flex-shrink: 0;
  opacity: 0.7;
}

.media-document:hover .download-icon {
  opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .message.media-message .message-content {
    max-width: 250px;
  }
  
  .media-image {
    max-height: 200px;
  }
  
  .media-document {
    padding: 10px;
  }
  
  .document-icon {
    font-size: 20px;
    margin-right: 10px;
  }
  
  .document-name {
    font-size: 13px;
  }
  
  .document-size {
    font-size: 11px;
  }
}

/* Sent message specific styles */
.message.media-message.sent .media-content {
  background-color: var(--primary-color-light);
  border-color: var(--primary-color);
}

.message.media-message.sent .media-document:hover {
  background-color: rgba(240, 79, 61, 0.1);
}

/* Received message specific styles */
.message.media-message.received .media-content {
  background-color: #f8f9fa;
  border-color: #e9ecef;
}

.message.media-message.received .media-document:hover {
  background-color: #e9ecef;
} 