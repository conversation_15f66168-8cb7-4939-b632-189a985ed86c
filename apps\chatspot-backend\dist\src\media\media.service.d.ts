import { Repository } from 'typeorm';
import { Media } from './media.entity';
import { S3ConfigService } from './s3-config.service';
export declare class MediaService {
    private mediaRepository;
    private s3ConfigService;
    private readonly maxImageSize;
    private readonly maxVideoSize;
    private readonly maxAudioSize;
    constructor(mediaRepository: Repository<Media>, s3ConfigService: S3ConfigService);
    private getMediaType;
    private isValidMimeType;
    uploadMedia(file: Express.Multer.File, uploadedBy: string): Promise<Media>;
    getMedia(id: string): Promise<Media>;
    getMediaSignedUrl(id: string, expiresIn?: number): Promise<{
        media: Media;
        signedUrl: string;
        expiresAt: Date;
    }>;
    deleteMedia(id: string, username: string): Promise<void>;
}
