"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var NotificationsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const fcm_token_entity_1 = require("./entities/fcm-token.entity");
const admin = require("firebase-admin");
const config_1 = require("@nestjs/config");
let NotificationsService = NotificationsService_1 = class NotificationsService {
    fcmTokenRepo;
    configService;
    logger = new common_1.Logger(NotificationsService_1.name);
    firebaseInitialized = false;
    constructor(fcmTokenRepo, configService) {
        this.fcmTokenRepo = fcmTokenRepo;
        this.configService = configService;
        this.initializeFirebase();
    }
    initializeFirebase() {
        try {
            if (admin.apps.length === 0) {
                const projectId = this.configService.get('FIREBASE_PROJECT_ID');
                const clientEmail = this.configService.get('FIREBASE_CLIENT_EMAIL');
                const privateKey = this.configService.get('FIREBASE_PRIVATE_KEY')?.replace(/\\n/g, '\n');
                this.logger.log(`Initializing Firebase with Project ID: ${projectId}`);
                if (!projectId || !clientEmail || !privateKey) {
                    this.logger.error('Firebase configuration is incomplete. Check your environment variables.');
                    this.logger.error(`Project ID: ${projectId ? 'Set' : 'Missing'}`);
                    this.logger.error(`Client Email: ${clientEmail ? 'Set' : 'Missing'}`);
                    this.logger.error(`Private Key: ${privateKey ? 'Set' : 'Missing'}`);
                    return;
                }
                admin.initializeApp({
                    credential: admin.credential.cert({
                        projectId,
                        clientEmail,
                        privateKey,
                    }),
                });
                this.firebaseInitialized = true;
                this.logger.log('Firebase Admin SDK initialized successfully');
            }
            else {
                this.firebaseInitialized = true;
                this.logger.log('Firebase Admin SDK already initialized');
            }
        }
        catch (error) {
            this.logger.error('Failed to initialize Firebase Admin SDK:', error);
            this.logger.error(error.message);
            if (error.code === 'app/invalid-credential') {
                this.logger.error('Invalid credentials. Check your service account key.');
            }
        }
    }
    async registerToken(username, token, deviceInfo) {
        try {
            const existingToken = await this.fcmTokenRepo.findOne({
                where: { username, token },
            });
            if (existingToken) {
                existingToken.is_active = true;
                existingToken.device_info = deviceInfo || existingToken.device_info;
                existingToken.last_used_at = new Date();
                return this.fcmTokenRepo.save(existingToken);
            }
            const fcmToken = this.fcmTokenRepo.create({
                username,
                token,
                device_info: deviceInfo,
                is_active: true,
                last_used_at: new Date(),
            });
            return this.fcmTokenRepo.save(fcmToken);
        }
        catch (error) {
            this.logger.error(`Failed to register FCM token for user ${username}:`, error);
            throw error;
        }
    }
    async getActiveTokensForUser(username) {
        try {
            return this.fcmTokenRepo.find({
                where: { username, is_active: true },
            });
        }
        catch (error) {
            this.logger.error(`Failed to get active FCM tokens for user ${username}:`, error);
            throw error;
        }
    }
    async deactivateToken(token) {
        try {
            await this.fcmTokenRepo.update({ token }, { is_active: false });
        }
        catch (error) {
            this.logger.error(`Failed to deactivate FCM token:`, error);
            throw error;
        }
    }
    async sendNotification(username, title, body, data = {}) {
        if (!this.firebaseInitialized) {
            this.logger.error('Firebase Admin SDK not initialized');
            this.initializeFirebase();
            if (!this.firebaseInitialized) {
                this.logger.error('Failed to initialize Firebase Admin SDK. Cannot send notification.');
                return false;
            }
        }
        try {
            const tokens = await this.getActiveTokensForUser(username);
            if (!tokens || tokens.length === 0) {
                this.logger.log(`No active FCM tokens found for user ${username}`);
                return false;
            }
            const tokenStrings = tokens.map(t => t.token);
            const successfulTokens = [];
            const failedTokens = [];
            for (const token of tokenStrings) {
                try {
                    const message = {
                        notification: {
                            title,
                            body,
                        },
                        data,
                        token,
                        webpush: {
                            fcm_options: {
                                link: `${this.configService.get('FRONTEND_URL', 'https://chatspot-messenger.netlify.app')}/chat`,
                            },
                            notification: {
                                icon: '/icons/app-icon-192x192.png',
                            },
                        },
                    };
                    const response = await admin.messaging().send(message);
                    if (response) {
                        successfulTokens.push(token);
                    }
                }
                catch (error) {
                    failedTokens.push(token);
                    this.logger.error(`Failed to send notification to token: ${token}`, error);
                }
            }
            this.logger.log(`Sent notification to ${successfulTokens.length} devices for user ${username}`);
            if (failedTokens.length > 0) {
                this.logger.warn(`Failed to send notification to ${failedTokens.length} devices for user ${username}`);
                for (const token of failedTokens) {
                    await this.deactivateToken(token);
                }
            }
            return successfulTokens.length > 0;
        }
        catch (error) {
            this.logger.error(`Failed to send notification to user ${username}:`, error);
            return false;
        }
    }
};
exports.NotificationsService = NotificationsService;
exports.NotificationsService = NotificationsService = NotificationsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(fcm_token_entity_1.FcmToken)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        config_1.ConfigService])
], NotificationsService);
//# sourceMappingURL=notifications.service.js.map