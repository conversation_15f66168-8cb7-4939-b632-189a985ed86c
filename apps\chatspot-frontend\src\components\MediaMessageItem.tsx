import React, { useState } from 'react';
import { mediaUploadService } from '../services/mediaUploadService';
import './MediaMessageItem.css';

interface MediaMessageItemProps {
  message: {
    message?: string;
    media_id?: string;
    media_uri?: string;
    media_name?: string;
    media_type?: string;
    media_size?: number;
    mediaId?: string;
    mediaUri?: string;
    mediaName?: string;
    mediaType?: string;
    mediaSize?: number;
    media_data?: {
      id: string;
      uri: string;
      name: string;
      type: string;
      size: number;
    };
    is_mine?: boolean;
    isMine?: boolean;
    timestamp?: number;
  };
  formatTime: (timestamp: number) => string;
  isLastInGroup: boolean;
}

const MediaMessageItem: React.FC<MediaMessageItemProps> = ({
  message,
  formatTime,
  isLastInGroup
}) => {
  const [imageError, setImageError] = useState(false);
  const [isImageLoading, setIsImageLoading] = useState(true);

  // Handle both camelCase and snake_case properties like mobile does
  const isMine = message.isMine || message.is_mine;
  
  // Use direct message properties like mobile does, handle both snake_case and camelCase
  const mediaType = message.media_type || message.mediaType;
  const isImage = true;
  // For now, we only support images like mobile
  const isDocument = false;

  // Debug logging
  console.log('🔍 MediaMessageItem Debug:', {
    message,
    messageKeys: Object.keys(message),
    mediaType,
    isImage,
    isDocument,
    startsWithImage: mediaType?.startsWith('image/'),
    willRenderImage: isImage,
    willRenderDocument: isDocument
  });

  const handleImageLoad = () => {
    setIsImageLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setIsImageLoading(false);
  };

  const handleDownload = () => {
    const mediaUri = message.media_uri || message.mediaUri;
    const mediaName = message.media_name || message.mediaName;
    if (mediaUri) {
      const link = document.createElement('a');
      link.href = mediaUri;
      link.download = mediaName || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const renderImage = () => {
    if (imageError) {
      return (
        <div className="media-error">
          <span>🖼️</span>
          <p>Failed to load image</p>
        </div>
      );
    }
console.log('message, ', message)
    return (
      <div className="media-image-container">
        {isImageLoading && (
          <div className="media-loading">
            <div className="loading-spinner"></div>
          </div>
        )}
        <img
          src={message.media_uri || message.mediaUri}
          alt={message.media_name || message.mediaName || 'Image'}
          className="media-image"
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={{ display: isImageLoading ? 'none' : 'block' }}
        />
      </div>
    );
  };

  const renderDocument = () => {
    const fileIcon = mediaUploadService.getFileIcon(mediaType || '');
    const fileSize = (message.media_size || message.mediaSize) ? mediaUploadService.formatFileSize(message.media_size || message.mediaSize || 0) : '';

    return (
      <div className="media-document" onClick={handleDownload}>
        <div className="document-icon">{fileIcon}</div>
        <div className="document-info">
          <div className="document-name">{message.media_name || message.mediaName || 'Document'}</div>
          {fileSize && <div className="document-size">{fileSize}</div>}
        </div>
        <div className="download-icon">⬇️</div>
      </div>
    );
  };

  return (
    <div
      className={`message ${isMine ? 'sent' : 'received'} media-message ${
        isLastInGroup ? 'last-in-group' : ''
      }`}
    >
      <div className="message-content">
        {message.message && (
          <p className="media-caption">{message.message}</p>
        )}
        
        <div className="media-content">
          {isImage && renderImage()}
          {!isImage && isDocument && renderDocument()}
          {!isImage && !isDocument && (
            <div className="media-error">
              <span>📄</span>
              <p>Unsupported file type</p>
            </div>
          )}
        </div>

        <div className="message-info">
          <span className="message-time">
            {message.timestamp ? formatTime(message.timestamp) : ''}
          </span>
        </div>
      </div>
    </div>
  );
};

export default MediaMessageItem; 