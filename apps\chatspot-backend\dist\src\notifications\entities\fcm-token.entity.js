"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FcmToken = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let FcmToken = class FcmToken {
    id;
    username;
    token;
    device_info;
    is_active;
    created_at;
    updated_at;
    last_used_at;
};
exports.FcmToken = FcmToken;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the FCM token record',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], FcmToken.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Username of the user',
        example: 'johndoe',
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], FcmToken.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Firebase Cloud Messaging token',
        example: 'fMqXXEFYQ-GNRaq7tUHXm6:APA91bHqH...',
    }),
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], FcmToken.prototype, "token", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Device information',
        example: 'Chrome 98.0.4758.102 on Windows 10',
        nullable: true,
    }),
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], FcmToken.prototype, "device_info", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether the token is active',
        example: true,
    }),
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], FcmToken.prototype, "is_active", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the token was created',
        example: '2023-10-15T14:30:00Z',
    }),
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], FcmToken.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the token was last updated',
        example: '2023-10-15T14:30:00Z',
    }),
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], FcmToken.prototype, "updated_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the token was last used',
        example: '2023-10-15T14:30:00Z',
        nullable: true,
    }),
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Object)
], FcmToken.prototype, "last_used_at", void 0);
exports.FcmToken = FcmToken = __decorate([
    (0, typeorm_1.Entity)('fcm_tokens')
], FcmToken);
//# sourceMappingURL=fcm-token.entity.js.map