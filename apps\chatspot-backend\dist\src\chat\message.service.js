"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const message_entity_1 = require("./message.entity");
let MessageService = class MessageService {
    messageRepo;
    constructor(messageRepo) {
        this.messageRepo = messageRepo;
    }
    async savePendingMessage(data) {
        const msg = this.messageRepo.create(data);
        return this.messageRepo.save(msg);
    }
    async markAsDelivered(id) {
        try {
            const result = await this.messageRepo.update(id, {
                status: 'delivered',
                delivered_at: new Date(),
            });
            if (result.affected === 0) {
                console.warn(`No message with ID ${id} was found to mark as delivered`);
            }
            return result;
        }
        catch (error) {
            console.error(`Error marking message ${id} as delivered:`, error);
            throw error;
        }
    }
    async delete(id) {
        return this.messageRepo.delete(id);
    }
    async getPendingMessagesForUser(userId) {
        console.warn('getPendingMessagesForUser is deprecated. Use getPendingMessagesForUsername instead.');
        return [];
    }
    async getPendingMessagesForUsername(username) {
        try {
            console.log(`Fetching pending messages for username: ${username}`);
            const messages = await this.messageRepo.find({
                where: { receiver_username: username, status: 'sent' },
            });
            console.log(`Found ${messages.length} pending messages for ${username}`);
            if (messages.length > 0) {
                messages.forEach((msg, index) => {
                    console.log(`Pending message ${index + 1}/${messages.length}: ID=${msg.id}, From=${msg.sender_username}, Type=${msg.type}`);
                });
            }
            return messages;
        }
        catch (error) {
            console.error(`Error fetching pending messages for ${username}:`, error);
            throw error;
        }
    }
    async getAllMessagesForUsername(username) {
        try {
            console.log(`Fetching all messages for username: ${username}`);
            const messages = await this.messageRepo.find({
                where: [
                    { sender_username: username },
                    { receiver_username: username }
                ],
                order: { timestamp: 'ASC' }
            });
            console.log(`Found ${messages.length} total messages for ${username}`);
            return messages;
        }
        catch (error) {
            console.error(`Error fetching all messages for ${username}:`, error);
            throw error;
        }
    }
    async getMessagesForConversation(username1, username2) {
        try {
            console.log(`Fetching messages for conversation between ${username1} and ${username2}`);
            const messages = await this.messageRepo.find({
                where: [
                    { sender_username: username1, receiver_username: username2 },
                    { sender_username: username2, receiver_username: username1 }
                ],
                order: { timestamp: 'ASC' }
            });
            console.log(`Found ${messages.length} messages for conversation`);
            return messages;
        }
        catch (error) {
            console.error(`Error fetching conversation messages:`, error);
            throw error;
        }
    }
    async deleteDeliveredMessages(username) {
        try {
            console.log(`Deleting delivered messages for user: ${username}`);
            const result = await this.messageRepo.delete({
                receiver_username: username,
                status: 'delivered'
            });
            console.log(`Deleted ${result.affected || 0} delivered messages for ${username}`);
            return result;
        }
        catch (error) {
            console.error(`Error deleting delivered messages for ${username}:`, error);
            throw error;
        }
    }
};
exports.MessageService = MessageService;
exports.MessageService = MessageService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(message_entity_1.Message)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], MessageService);
//# sourceMappingURL=message.service.js.map