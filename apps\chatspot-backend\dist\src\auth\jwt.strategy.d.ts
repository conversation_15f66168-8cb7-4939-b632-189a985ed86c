import { Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { RefreshTokenService } from './refresh-token.service';
declare const JwtStrategy_base: new (...args: [opt: import("passport-jwt").StrategyOptionsWithRequest] | [opt: import("passport-jwt").StrategyOptionsWithoutRequest]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class JwtStrategy extends JwtStrategy_base {
    private configService;
    private refreshTokenService;
    constructor(configService: ConfigService, refreshTokenService: RefreshTokenService);
    validate(payload: any): Promise<{
        userId: any;
        username: any;
        isAdmin: any;
    }>;
}
export {};
