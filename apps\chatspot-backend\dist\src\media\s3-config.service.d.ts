import { ConfigService } from '@nestjs/config';
import { S3Client } from '@aws-sdk/client-s3';
export declare class S3ConfigService {
    private configService;
    private s3Client;
    private bucketName;
    private useS3;
    constructor(configService: ConfigService);
    getS3Client(): S3Client;
    getBucketName(): string;
    isS3Enabled(): boolean;
    getS3Url(key: string): string;
    generatePresignedGetUrl(key: string, expiresIn?: number): Promise<string>;
    generatePresignedPutUrl(key: string, contentType: string, expiresIn?: number): Promise<string>;
}
