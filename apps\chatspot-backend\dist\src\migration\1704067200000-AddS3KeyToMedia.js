"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddS3KeyToMedia1704067200000 = void 0;
const typeorm_1 = require("typeorm");
class AddS3KeyToMedia1704067200000 {
    name = 'AddS3KeyToMedia1704067200000';
    async up(queryRunner) {
        await queryRunner.addColumn('media', new typeorm_1.TableColumn({
            name: 's3_key',
            type: 'varchar',
            isNullable: true,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('media', 's3_key');
    }
}
exports.AddS3KeyToMedia1704067200000 = AddS3KeyToMedia1704067200000;
//# sourceMappingURL=1704067200000-AddS3KeyToMedia.js.map