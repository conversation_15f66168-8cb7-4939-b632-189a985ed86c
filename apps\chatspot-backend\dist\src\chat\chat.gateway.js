"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ChatGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const jwt_1 = require("@nestjs/jwt");
const common_1 = require("@nestjs/common");
const message_service_1 = require("./message.service");
const create_message_dto_1 = require("./dto/create-message.dto");
const users_service_1 = require("../auth/users.service");
const notifications_service_1 = require("../notifications/notifications.service");
let ChatGateway = ChatGateway_1 = class ChatGateway {
    jwtService;
    messageService;
    usersService;
    notificationsService;
    server;
    logger = new common_1.Logger(ChatGateway_1.name);
    usernameSocketMap = new Map();
    constructor(jwtService, messageService, usersService, notificationsService) {
        this.jwtService = jwtService;
        this.messageService = messageService;
        this.usersService = usersService;
        this.notificationsService = notificationsService;
    }
    async handleConnection(socket) {
        console.log('Client connected:', socket.id);
        try {
            const token = socket.handshake.auth.token;
            if (!token)
                throw new common_1.UnauthorizedException('Token not provided');
            const payload = this.jwtService.verify(token);
            const username = payload.username;
            this.usernameSocketMap.set(username, socket.id);
            socket.data.username = username;
            console.log(`User connected: ${username}`);
        }
        catch (err) {
            console.error('Connection error:', err.message);
            socket.disconnect();
        }
    }
    handleDisconnect(socket) {
        const username = socket.data.username;
        if (username) {
            console.log(`User disconnected: ${username}`);
            this.usernameSocketMap.delete(username);
        }
    }
    invalidateUserSession(username, reason = 'logged_in_elsewhere') {
        const socketId = this.usernameSocketMap.get(username);
        if (socketId) {
            console.log(`Invalidating session for user ${username} - reason: ${reason}`);
            this.server.to(socketId).emit('session_invalidated', {
                reason,
                message: 'You have been logged out because you signed in from another device.',
                timestamp: new Date().toISOString()
            });
            const socket = this.server.sockets.sockets.get(socketId);
            if (socket) {
                socket.disconnect(true);
            }
            this.usernameSocketMap.delete(username);
            return true;
        }
        else {
            console.log(`User ${username} is not connected, cannot invalidate session`);
            return false;
        }
    }
    notifyMessageDelivered(senderUsername, messageId, receiverUsername, clientMessageId) {
        const senderSocketId = this.usernameSocketMap.get(senderUsername);
        if (senderSocketId) {
            console.log(`Notifying sender ${senderUsername} about message delivery via REST API sync`);
            this.server.to(senderSocketId).emit('message_delivered', {
                message_id: messageId,
                client_message_id: clientMessageId,
                receiver_username: receiverUsername,
                status: 'delivered',
                delivery_method: 'rest_api_sync'
            });
            return true;
        }
        else {
            console.log(`Sender ${senderUsername} is not connected, cannot notify about delivery`);
            return false;
        }
    }
    handleMessage(data, socket) {
        console.log('Message received, type:', data.type);
        this.processMessage(data, socket).catch(error => {
            console.error('Error processing message:', error.message);
            socket.emit('error', { message: 'Failed to send message: ' + error.message });
        });
        if (data.type === 'text' || data.type === 'media') {
            console.log(`Returning acknowledgment for ${data.type} message`);
            return { status: 'acknowledged', message: 'Message received by server' };
        }
        console.log('No acknowledgment for non-text message type:', data.type);
        return;
    }
    async handleDeliveryConfirmed(data, socket) {
        try {
            const username = socket.data.username;
            if (!username) {
                throw new common_1.UnauthorizedException('User not authenticated');
            }
            console.log(`Delivery confirmation received from ${username} for message ${data.message_id}`);
            try {
                await this.messageService.delete(data.message_id);
                console.log(`Message ${data.message_id} deleted from backend after delivery confirmation`);
            }
            catch (deleteError) {
                console.error(`Failed to delete message ${data.message_id} after delivery confirmation:`, deleteError);
            }
        }
        catch (error) {
            console.error('Error handling delivery confirmation:', error.message);
            socket.emit('error', { message: 'Failed to process delivery confirmation: ' + error.message });
        }
    }
    async processMessage(data, socket) {
        try {
            const senderUsername = socket.data.username;
            if (!senderUsername) {
                throw new common_1.UnauthorizedException('User not authenticated');
            }
            const receiverUsername = data.receiver_username;
            const message = {
                ...data,
                sender_username: senderUsername,
                type: data.type || 'text',
            };
            try {
                await this.usersService.findUserIdByUsername(receiverUsername);
            }
            catch (error) {
                throw new common_1.NotFoundException(`User with username "${receiverUsername}" not found`);
            }
            const receiverSocketId = this.usernameSocketMap.get(receiverUsername);
            if (message.type === 'text' || message.type === 'media') {
                const saved = await this.messageService.savePendingMessage(message);
                if (receiverSocketId) {
                    console.log(`Delivering text message to ${receiverUsername} (ID: ${saved.id})`);
                    this.server.to(receiverSocketId).emit('message', {
                        ...message,
                        id: saved.id,
                        client_message_id: message.client_message_id
                    }, (deliveryAck) => {
                        console.log('Delivery acknowledgment received:');
                        if (deliveryAck && typeof deliveryAck === 'object') {
                            console.log(`Message ${saved.id} received acknowledgment from ${receiverUsername}`);
                            const senderSocketId = this.usernameSocketMap.get(senderUsername);
                            if (senderSocketId) {
                                console.log(`Notifying sender ${senderUsername} about message delivery`);
                                this.server.to(senderSocketId).emit('message_delivered', {
                                    message_id: saved.id,
                                    client_message_id: message.client_message_id,
                                    receiver_username: receiverUsername,
                                    status: 'delivered'
                                });
                            }
                        }
                        else {
                            console.log(`Message ${saved.id} received invalid acknowledgment from ${receiverUsername}:`, deliveryAck);
                        }
                    });
                    try {
                        await this.messageService.delete(saved.id);
                        console.log(`Message ${saved.id} deleted from pending messages after delivery`);
                    }
                    catch (deleteError) {
                        console.error(`Failed to delete message ${saved.id} after delivery:`, deleteError);
                    }
                }
                else {
                    this.logger.log(`User ${receiverUsername} is offline, text message saved for later delivery (ID: ${saved.id})`);
                    try {
                        await this.notificationsService.sendNotification(receiverUsername, `New message from ${senderUsername}`, message.message.length > 100 ? message.message.substring(0, 97) + '...' : message.message, {
                            type: 'new_message',
                            sender: senderUsername,
                            messageId: saved.id,
                            timestamp: Date.now().toString(),
                        });
                        this.logger.log(`Push notification sent to offline user ${receiverUsername}`);
                    }
                    catch (notificationError) {
                        this.logger.error(`Failed to send push notification to ${receiverUsername}:`, notificationError);
                    }
                }
            }
            else {
                if (receiverSocketId) {
                    console.log(`Delivering ${message.type} message to ${receiverUsername} (not saved)`);
                    this.server.to(receiverSocketId).emit('message', message);
                }
            }
        }
        catch (error) {
            console.error('Error handling message:', error.message);
            throw error;
        }
    }
};
exports.ChatGateway = ChatGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], ChatGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('message'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_message_dto_1.CreateMessageDto,
        socket_io_1.Socket]),
    __metadata("design:returntype", Object)
], ChatGateway.prototype, "handleMessage", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('delivery_confirmed'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, socket_io_1.Socket]),
    __metadata("design:returntype", Promise)
], ChatGateway.prototype, "handleDeliveryConfirmed", null);
exports.ChatGateway = ChatGateway = ChatGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: '*',
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH', 'HEAD'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
            credentials: true,
        },
    }),
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        message_service_1.MessageService,
        users_service_1.UsersService,
        notifications_service_1.NotificationsService])
], ChatGateway);
//# sourceMappingURL=chat.gateway.js.map