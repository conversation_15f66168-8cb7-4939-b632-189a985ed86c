"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MediaService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const media_entity_1 = require("./media.entity");
const s3_config_service_1 = require("./s3-config.service");
const sharp = require("sharp");
let MediaService = class MediaService {
    mediaRepository;
    s3ConfigService;
    maxImageSize = 10 * 1024 * 1024;
    maxVideoSize = 100 * 1024 * 1024;
    maxAudioSize = 50 * 1024 * 1024;
    constructor(mediaRepository, s3ConfigService) {
        this.mediaRepository = mediaRepository;
        this.s3ConfigService = s3ConfigService;
    }
    getMediaType(mimeType) {
        if (mimeType.startsWith('image/'))
            return 'image';
        if (mimeType.startsWith('video/'))
            return 'video';
        if (mimeType.startsWith('audio/'))
            return 'audio';
        throw new Error('Unsupported media type. Only images, videos, and audio are supported.');
    }
    isValidMimeType(mimeType) {
        const allowedTypes = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif',
            'video/mp4', 'video/mov', 'video/avi', 'video/quicktime', 'video/3gpp', 'video/webm',
            'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/aac', 'audio/m4a', 'audio/ogg', 'audio/webm',
        ];
        return allowedTypes.includes(mimeType);
    }
    async uploadMedia(file, uploadedBy) {
        const mediaType = this.getMediaType(file.mimetype);
        let maxSize;
        let sizeLabel;
        switch (mediaType) {
            case 'image':
                maxSize = this.maxImageSize;
                sizeLabel = '10MB';
                break;
            case 'video':
                maxSize = this.maxVideoSize;
                sizeLabel = '100MB';
                break;
            case 'audio':
                maxSize = this.maxAudioSize;
                sizeLabel = '50MB';
                break;
            default:
                maxSize = this.maxImageSize;
                sizeLabel = '10MB';
        }
        if (file.size > maxSize) {
            throw new common_1.BadRequestException(`File size exceeds maximum limit of ${sizeLabel} for ${mediaType} files`);
        }
        if (!this.isValidMimeType(file.mimetype)) {
            throw new common_1.BadRequestException('Unsupported file type');
        }
        const mediaType = this.getMediaType(file.mimetype);
        const fileExtension = file.originalname.split('.').pop();
        const s3Key = `media/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`;
        const s3Client = this.s3ConfigService.getS3Client();
        const bucketName = this.s3ConfigService.getBucketName();
        try {
            const { PutObjectCommand } = await Promise.resolve().then(() => require('@aws-sdk/client-s3'));
            const command = new PutObjectCommand({
                Bucket: bucketName,
                Key: s3Key,
                Body: file.buffer,
                ContentType: file.mimetype,
            });
            await s3Client.send(command);
        }
        catch (error) {
            console.error('Error uploading to S3:', error);
            throw new common_1.BadRequestException('Failed to upload file to storage');
        }
        let width = null;
        let height = null;
        if (mediaType === 'image') {
            try {
                const metadata = await sharp(file.buffer).metadata();
                width = metadata.width || null;
                height = metadata.height || null;
            }
            catch (error) {
                console.error('Error processing image:', error);
            }
        }
        const media = this.mediaRepository.create({
            original_filename: file.originalname,
            filename: s3Key.split('/').pop() || file.originalname,
            mime_type: file.mimetype,
            file_size: file.size,
            media_type: mediaType,
            file_path: `https://${bucketName}.s3.amazonaws.com/${s3Key}`,
            uploaded_by: uploadedBy,
            width,
            height,
            duration: null,
            thumbnail_path: null,
            s3_key: s3Key,
        });
        return this.mediaRepository.save(media);
    }
    async getMedia(id) {
        const media = await this.mediaRepository.findOne({ where: { id } });
        if (!media) {
            throw new common_1.NotFoundException('Media not found');
        }
        return media;
    }
    async getMediaSignedUrl(id, expiresIn = 3600) {
        const media = await this.getMedia(id);
        const expiresAt = new Date(Date.now() + expiresIn * 1000);
        if (!media.s3_key) {
            throw new common_1.NotFoundException('Media file S3 key not found');
        }
        const signedUrl = await this.s3ConfigService.generatePresignedGetUrl(media.s3_key, expiresIn);
        return {
            media,
            signedUrl,
            expiresAt,
        };
    }
    async deleteMedia(id, username) {
        const media = await this.getMedia(id);
        if (media.uploaded_by !== username) {
            throw new common_1.ForbiddenException('You can only delete your own media files');
        }
        if (media.s3_key) {
            try {
                const { DeleteObjectCommand } = await Promise.resolve().then(() => require('@aws-sdk/client-s3'));
                const s3Client = this.s3ConfigService.getS3Client();
                const bucketName = this.s3ConfigService.getBucketName();
                const command = new DeleteObjectCommand({
                    Bucket: bucketName,
                    Key: media.s3_key,
                });
                await s3Client.send(command);
                console.log(`Successfully deleted file from S3: ${media.s3_key}`);
            }
            catch (error) {
                console.error('Error deleting file from S3:', error);
            }
        }
        await this.mediaRepository.delete(id);
        console.log(`Successfully deleted media record from database: ${id}`);
    }
};
exports.MediaService = MediaService;
exports.MediaService = MediaService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(media_entity_1.Media)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        s3_config_service_1.S3ConfigService])
], MediaService);
//# sourceMappingURL=media.service.js.map