import { NotificationsService } from './notifications.service';
import { RegisterTokenDto } from './dto/register-token.dto';
export declare class NotificationsController {
    private readonly notificationsService;
    private readonly logger;
    constructor(notificationsService: NotificationsService);
    registerToken(registerTokenDto: RegisterTokenDto): Promise<{
        success: boolean;
        message: string;
        data: {
            id: string;
            username: string;
            created_at: Date;
        };
    }>;
    sendTestNotification(username: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
