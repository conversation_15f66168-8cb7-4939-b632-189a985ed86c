import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { MessageService } from './message.service';
import { CreateMessageDto } from './dto/create-message.dto';
import { UsersService } from '../auth/users.service';
import { NotificationsService } from '../notifications/notifications.service';
export declare class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
    private readonly jwtService;
    private readonly messageService;
    private readonly usersService;
    private readonly notificationsService;
    server: Server;
    private readonly logger;
    private usernameSocketMap;
    constructor(jwtService: JwtService, messageService: MessageService, usersService: UsersService, notificationsService: NotificationsService);
    handleConnection(socket: Socket): Promise<void>;
    handleDisconnect(socket: Socket): void;
    invalidateUserSession(username: string, reason?: string): boolean;
    notifyMessageDelivered(senderUsername: string, messageId: string, receiverUsername: string, clientMessageId?: string | null): boolean;
    handleMessage(data: CreateMessageDto, socket: Socket): {
        status: string;
        message: string;
    } | void;
    handleDeliveryConfirmed(data: {
        message_id: string;
    }, socket: Socket): Promise<void>;
    private processMessage;
}
