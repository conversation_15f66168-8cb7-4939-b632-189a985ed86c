import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { RefreshToken } from './refresh-token.entity';
export declare class RefreshTokenService {
    private refreshTokenRepo;
    private configService;
    constructor(refreshTokenRepo: Repository<RefreshToken>, configService: ConfigService);
    generateRefreshToken(userId: string, deviceInfo?: {
        deviceId?: string;
        userAgent?: string;
        ipAddress?: string;
    }): Promise<RefreshToken>;
    validateRefreshToken(token: string): Promise<RefreshToken | null>;
    revokeRefreshToken(token: string): Promise<void>;
    revokeAllUserTokens(userId: string): Promise<void>;
    hasValidTokens(userId: string): Promise<boolean>;
    rotateRefreshToken(oldToken: string, userId: string): Promise<RefreshToken>;
    cleanupExpiredTokens(): Promise<void>;
    private parseExpirationTime;
}
