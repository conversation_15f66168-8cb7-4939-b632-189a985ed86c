[{"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/build/generated/autolinking/src/main/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\C_\\Users\\milap\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/build/generated/autolinking/src/main/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\RNBootSplashSpec-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\RNBootSplashSpec-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\RNBootSplashSpec-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\react\\renderer\\components\\RNBootSplashSpec\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\react\\renderer\\components\\RNBootSplashSpec\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\react\\renderer\\components\\RNBootSplashSpec\\Props.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\Props.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\Props.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\react\\renderer\\components\\RNBootSplashSpec\\RNBootSplashSpecJSI-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\RNBootSplashSpecJSI-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\RNBootSplashSpecJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\react\\renderer\\components\\RNBootSplashSpec\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\react\\renderer\\components\\RNBootSplashSpec\\States.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\States.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\States.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\States.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\rngesturehandler_codegen-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\RNImagePickerSpec-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\RNImagePickerSpec-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\RNImagePickerSpec-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\Props.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\Props.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\Props.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\b67162b4a9e608cf7419840e3175142c\\RNImagePickerSpecJSI-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\RNImagePickerSpecJSI-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\RNImagePickerSpecJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\States.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\States.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\States.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8d39bdc0dcc4b203eedd3692ce7b54dc\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8d39bdc0dcc4b203eedd3692ce7b54dc\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\a0ec598468663b2d6a69556579942662\\safeareacontext\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\323f7d9d52eeda816554b975c1d38497\\components\\safeareacontext\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\2b89d2a8d0c3370f35c9440104b6fc95\\renderer\\components\\safeareacontext\\Props.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\323f7d9d52eeda816554b975c1d38497\\components\\safeareacontext\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\323f7d9d52eeda816554b975c1d38497\\components\\safeareacontext\\States.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\dbd25e3a7d04ea050205e9466bc2d91d\\safeareacontextJSI-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\628bf02a2c026c0f5b3ed26127f7c630\\codegen\\jni\\safeareacontext-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\d98050694309b0c4fd52f28699077686\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\d98050694309b0c4fd52f28699077686\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\98d6588fcde434fa895b269beff52db0\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6febaef0ac6230c1eec188512ef41675\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\d98050694309b0c4fd52f28699077686\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6febaef0ac6230c1eec188512ef41675\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\d98050694309b0c4fd52f28699077686\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\db6f74005a0470bb8a4dd5c6dbe8420f\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\d700831a64592c0c42d2c0a014a04e12\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\4b09e3eb4687961a27de5eb36bdcdccd\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\11ae63b6958665d99abfc379b72c9f6f\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\4b09e3eb4687961a27de5eb36bdcdccd\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\4b09e3eb4687961a27de5eb36bdcdccd\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\d700831a64592c0c42d2c0a014a04e12\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\2f825b0a62fcb8ce44579b354e4de92f\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\e199a0a3ec8bc2f2f7a8fbc8546a88b8\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\2f825b0a62fcb8ce44579b354e4de92f\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\e199a0a3ec8bc2f2f7a8fbc8546a88b8\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\rnsvg.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\904c8935d63527fe2979d4667f886b5c\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\31d7e11373188dc6ad9b9f19da4f5104\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\fb0b1bfb3749a3f7d52e0a4537b5cd7f\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\31d7e11373188dc6ad9b9f19da4f5104\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\fb0b1bfb3749a3f7d52e0a4537b5cd7f\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\904c8935d63527fe2979d4667f886b5c\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\RNVectorIconsSpec-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\796ad459e5c56493b6ccb0d610a9a963\\RNVectorIconsSpecJSI-generated.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp"}, {"directory": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/5d6n2e2o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp.o -c C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp", "file": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp"}]