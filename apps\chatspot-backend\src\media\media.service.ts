import { Injectable, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Media, MediaType } from './media.entity';
import { S3ConfigService } from './s3-config.service';
import * as sharp from 'sharp';

@Injectable()
export class MediaService {
  private readonly maxImageSize = 10 * 1024 * 1024; // 10MB for images
  private readonly maxVideoSize = 100 * 1024 * 1024; // 100MB for videos
  private readonly maxAudioSize = 50 * 1024 * 1024; // 50MB for audio

  constructor(
    @InjectRepository(Media)
    private mediaRepository: Repository<Media>,
    private s3ConfigService: S3ConfigService,
  ) {
    // S3 is now mandatory for media storage
  }

  private getMediaType(mimeType: string): MediaType {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    throw new Error('Unsupported media type. Only images, videos, and audio are supported.');
  }

  private isValidMimeType(mimeType: string): boolean {
    const allowedTypes = [
      // Images
      'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif',
      // Videos
      'video/mp4', 'video/mov', 'video/avi', 'video/quicktime', 'video/3gpp', 'video/webm',
      // Audio
      'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/aac', 'audio/m4a', 'audio/ogg', 'audio/webm',
    ];
    return allowedTypes.includes(mimeType);
  }

  async uploadMedia(
    file: Express.Multer.File,
    uploadedBy: string,
  ): Promise<Media> {
    // Validate file size based on media type
    const mediaType = this.getMediaType(file.mimetype);
    let maxSize: number;
    let sizeLabel: string;

    switch (mediaType) {
      case 'image':
        maxSize = this.maxImageSize;
        sizeLabel = '10MB';
        break;
      case 'video':
        maxSize = this.maxVideoSize;
        sizeLabel = '100MB';
        break;
      case 'audio':
        maxSize = this.maxAudioSize;
        sizeLabel = '50MB';
        break;
      default:
        maxSize = this.maxImageSize;
        sizeLabel = '10MB';
    }

    if (file.size > maxSize) {
      throw new BadRequestException(
        `File size exceeds maximum limit of ${sizeLabel} for ${mediaType} files`,
      );
    }

    // Validate MIME type
    if (!this.isValidMimeType(file.mimetype)) {
      throw new BadRequestException('Unsupported file type');
    }

    const mediaType = this.getMediaType(file.mimetype);

    // Generate unique S3 key
    const fileExtension = file.originalname.split('.').pop();
    const s3Key = `media/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`;

    // Upload to S3
    const s3Client = this.s3ConfigService.getS3Client();
    const bucketName = this.s3ConfigService.getBucketName();

    try {
      const { PutObjectCommand } = await import('@aws-sdk/client-s3');
      const command = new PutObjectCommand({
        Bucket: bucketName,
        Key: s3Key,
        Body: file.buffer,
        ContentType: file.mimetype,
      });

      await s3Client.send(command);
    } catch (error) {
      console.error('Error uploading to S3:', error);
      throw new BadRequestException('Failed to upload file to storage');
    }

    let width: number | null = null;
    let height: number | null = null;

    // Process images
    if (mediaType === 'image') {
      try {
        const metadata = await sharp(file.buffer).metadata();
        width = metadata.width || null;
        height = metadata.height || null;
      } catch (error) {
        console.error('Error processing image:', error);
      }
    }

    // Create media record
    const media = this.mediaRepository.create({
      original_filename: file.originalname,
      filename: s3Key.split('/').pop() || file.originalname,
      mime_type: file.mimetype,
      file_size: file.size,
      media_type: mediaType,
      file_path: `https://${bucketName}.s3.amazonaws.com/${s3Key}`,
      uploaded_by: uploadedBy,
      width,
      height,
      duration: null, // TODO: Extract duration for video/audio files
      thumbnail_path: null,
      s3_key: s3Key,
    });

    return this.mediaRepository.save(media);
  }

  async getMedia(id: string): Promise<Media> {
    const media = await this.mediaRepository.findOne({ where: { id } });
    if (!media) {
      throw new NotFoundException('Media not found');
    }
    return media;
  }

  /**
   * Generate a signed URL for accessing a media file
   * @param id Media ID
   * @param expiresIn Expiration time in seconds (default: 1 hour)
   * @returns Object containing media info and signed URL
   */
  async getMediaSignedUrl(id: string, expiresIn: number = 3600): Promise<{
    media: Media;
    signedUrl: string;
    expiresAt: Date;
  }> {
    const media = await this.getMedia(id);
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    if (!media.s3_key) {
      throw new NotFoundException('Media file S3 key not found');
    }

    // Generate S3 presigned URL
    const signedUrl = await this.s3ConfigService.generatePresignedGetUrl(
      media.s3_key,
      expiresIn
    );

    return {
      media,
      signedUrl,
      expiresAt,
    };
  }

  /**
   * Delete media from both S3 and database
   * @param id Media ID
   * @param username Username of the user attempting to delete
   */
  async deleteMedia(id: string, username: string): Promise<void> {
    const media = await this.getMedia(id);

    // Check if user is the owner of the media
    if (media.uploaded_by !== username) {
      throw new ForbiddenException('You can only delete your own media files');
    }

    // Delete from S3 if s3_key exists
    if (media.s3_key) {
      try {
        const { DeleteObjectCommand } = await import('@aws-sdk/client-s3');
        const s3Client = this.s3ConfigService.getS3Client();
        const bucketName = this.s3ConfigService.getBucketName();

        const command = new DeleteObjectCommand({
          Bucket: bucketName,
          Key: media.s3_key,
        });

        await s3Client.send(command);
        console.log(`Successfully deleted file from S3: ${media.s3_key}`);
      } catch (error) {
        console.error('Error deleting file from S3:', error);
        // Continue with database deletion even if S3 deletion fails
      }
    }

    // Delete from database
    await this.mediaRepository.delete(id);
    console.log(`Successfully deleted media record from database: ${id}`);
  }
}
