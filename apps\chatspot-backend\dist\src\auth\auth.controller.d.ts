import { AuthService } from './auth.service';
import { AuthCredentialsDto, AuthResponseDto, RefreshTokenDto, UserDto } from './dto';
import { Request as ExpressRequest } from 'express';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    register(authCredentialsDto: AuthCredentialsDto): Promise<AuthResponseDto>;
    login(authCredentialsDto: AuthCredentialsDto, req: ExpressRequest): Promise<AuthResponseDto>;
    getCurrentUser(req: any): Promise<UserDto>;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<AuthResponseDto>;
    logout(refreshTokenDto: RefreshTokenDto): Promise<{
        message: string;
    }>;
    logoutAll(req: any): Promise<{
        message: string;
    }>;
    validateSession(req: any): Promise<{
        valid: boolean;
        message: string;
    }>;
}
