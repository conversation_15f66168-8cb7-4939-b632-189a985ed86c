{"version": 3, "file": "s3-config.service.js", "sourceRoot": "", "sources": ["../../../src/media/s3-config.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAC/C,kDAAkF;AAClF,wEAA6D;AAGtD,IAAM,eAAe,GAArB,MAAM,eAAe;IAKN;IAJZ,QAAQ,CAAW;IACnB,UAAU,CAAS;IACnB,KAAK,CAAU;IAEvB,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAE9C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;QAEhE,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAChE,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAExE,IAAI,CAAC,WAAW,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;QACpF,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,oBAAQ,CAAC;YAC3B,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,WAAW;YAC3D,WAAW,EAAE;gBACX,WAAW;gBACX,eAAe;aAChB;SACF,CAAC,CAAC;IACL,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,QAAQ,CAAC,GAAW;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,WAAW,CAAC;QACnE,OAAO,WAAW,IAAI,CAAC,UAAU,OAAO,MAAM,kBAAkB,GAAG,EAAE,CAAC;IACxE,CAAC;IAQD,KAAK,CAAC,uBAAuB,CAAC,GAAW,EAAE,YAAoB,IAAI;QACjE,MAAM,OAAO,GAAG,IAAI,4BAAgB,CAAC;YACnC,MAAM,EAAE,IAAI,CAAC,UAAU;YACvB,GAAG,EAAE,GAAG;SACT,CAAC,CAAC;QAEH,OAAO,IAAA,mCAAY,EAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;IAC7D,CAAC;IASD,KAAK,CAAC,uBAAuB,CAAC,GAAW,EAAE,WAAmB,EAAE,YAAoB,IAAI;QACtF,MAAM,OAAO,GAAG,IAAI,4BAAgB,CAAC;YACnC,MAAM,EAAE,IAAI,CAAC,UAAU;YACvB,GAAG,EAAE,GAAG;YACR,WAAW,EAAE,WAAW;SACzB,CAAC,CAAC;QAEH,OAAO,IAAA,mCAAY,EAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;IAC7D,CAAC;CACF,CAAA;AA1EY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;qCAMwB,sBAAa;GALrC,eAAe,CA0E3B"}